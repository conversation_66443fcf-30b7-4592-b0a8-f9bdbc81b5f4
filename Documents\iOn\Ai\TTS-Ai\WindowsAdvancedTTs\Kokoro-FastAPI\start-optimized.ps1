# Kokoro-FastAPI Optimized Startup Script for Windows
# This script sets up and starts the Kokoro TTS server with optimal settings

param(
    [switch]$Build,
    [switch]$Clean,
    [switch]$Logs,
    [switch]$Stop,
    [string]$Profile = "cpu"
)

$ErrorActionPreference = "Stop"

Write-Host "🎤 Kokoro-FastAPI TTS Server Setup" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Create necessary directories
$directories = @("models", "output", "logs")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "📁 Created directory: $dir" -ForegroundColor Yellow
    }
}

# Handle different operations
switch ($true) {
    $Stop {
        Write-Host "🛑 Stopping Kokoro TTS services..." -ForegroundColor Yellow
        docker-compose -f docker-compose-optimized.yml down
        Write-Host "✅ Services stopped" -ForegroundColor Green
        exit 0
    }
    
    $Clean {
        Write-Host "🧹 Cleaning up containers and images..." -ForegroundColor Yellow
        docker-compose -f docker-compose-optimized.yml down --rmi all --volumes
        docker system prune -f
        Write-Host "✅ Cleanup complete" -ForegroundColor Green
        exit 0
    }
    
    $Logs {
        Write-Host "📋 Showing service logs..." -ForegroundColor Yellow
        docker-compose -f docker-compose-optimized.yml logs -f
        exit 0
    }
}

# Check system resources
$cpu = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
$memory = Get-WmiObject -Class Win32_ComputerSystem
$memoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)

Write-Host "💻 System Information:" -ForegroundColor Cyan
Write-Host "   CPU: $($cpu.Name)" -ForegroundColor White
Write-Host "   Cores: $($cpu.NumberOfCores)" -ForegroundColor White
Write-Host "   Memory: $memoryGB GB" -ForegroundColor White

# Optimize environment variables based on system
$env:ONNX_NUM_THREADS = [math]::Min($cpu.NumberOfCores * 2, 16)
$env:OMP_NUM_THREADS = $cpu.NumberOfCores
Write-Host "🔧 Optimized for $($cpu.NumberOfCores) CPU cores" -ForegroundColor Green

# Build if requested or if images don't exist
if ($Build -or !(docker images -q kokoro-tts-optimized_kokoro-tts)) {
    Write-Host "🔨 Building Kokoro TTS container..." -ForegroundColor Yellow
    docker-compose -f docker-compose-optimized.yml build --no-cache
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Build complete" -ForegroundColor Green
}

# Start the services
Write-Host "🚀 Starting Kokoro TTS server..." -ForegroundColor Yellow
docker-compose -f docker-compose-optimized.yml up -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Kokoro TTS server started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Service URLs:" -ForegroundColor Cyan
    Write-Host "   API: http://localhost:8880" -ForegroundColor White
    Write-Host "   Docs: http://localhost:8880/docs" -ForegroundColor White
    Write-Host "   Web UI: http://localhost:8880/web" -ForegroundColor White
    Write-Host ""
    Write-Host "📖 Usage Examples:" -ForegroundColor Cyan
    Write-Host "   View logs: .\start-optimized.ps1 -Logs" -ForegroundColor White
    Write-Host "   Stop server: .\start-optimized.ps1 -Stop" -ForegroundColor White
    Write-Host "   Rebuild: .\start-optimized.ps1 -Build" -ForegroundColor White
    Write-Host ""
    Write-Host "⏳ Waiting for server to be ready..." -ForegroundColor Yellow
    
    # Wait for health check
    $maxAttempts = 30
    $attempt = 0
    do {
        Start-Sleep 2
        $attempt++
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8880/health" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Server is ready!" -ForegroundColor Green
                break
            }
        } catch {
            Write-Host "." -NoNewline -ForegroundColor Yellow
        }
    } while ($attempt -lt $maxAttempts)
    
    if ($attempt -ge $maxAttempts) {
        Write-Host ""
        Write-Host "⚠️  Server may still be starting. Check logs with: .\start-optimized.ps1 -Logs" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Failed to start services" -ForegroundColor Red
    exit 1
}
