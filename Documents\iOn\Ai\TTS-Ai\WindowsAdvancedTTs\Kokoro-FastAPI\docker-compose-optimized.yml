name: kokoro-tts-optimized
services:
  kokoro-tts:
    build:
      context: .
      dockerfile: docker/cpu/Dockerfile
    volumes:
      - ./api:/app/api
      - ./models:/app/models  # Persistent model storage
      - ./output:/app/output  # Output directory for generated audio
    ports:
      - "8880:8880"  # Main API port
      - "8881:8881"  # Optional secondary port
    environment:
      - PYTHONPATH=/app:/app/api
      - PYTHONUNBUFFERED=1
      
      # CPU Optimization Settings
      - ONNX_NUM_THREADS=12  # Optimized for modern CPUs
      - ONNX_INTER_OP_THREADS=6  # Higher inter-op for parallel operations
      - ONNX_EXECUTION_MODE=parallel
      - ONNX_OPTIMIZATION_LEVEL=all
      - ONNX_MEMORY_PATTERN=true
      - ONNX_ARENA_EXTEND_STRATEGY=kNextPowerOfTwo
      
      # Performance tuning
      - OMP_NUM_THREADS=12
      - MKL_NUM_THREADS=12
      - NUMBA_NUM_THREADS=12
      
      # Model settings
      - MODEL_CACHE_DIR=/app/models
      - PRELOAD_MODEL=true
      - ENABLE_STREAMING=true
      
      # API Configuration
      - API_HOST=0.0.0.0
      - API_PORT=8880
      - LOG_LEVEL=INFO
      
      # Memory optimization
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
      
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8880/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Web UI (optional - uncomment if you want the web interface)
  # web-ui:
  #   build:
  #     context: ./web
  #   ports:
  #     - "8882:80"
  #   depends_on:
  #     - kokoro-tts
  #   environment:
  #     - API_URL=http://kokoro-tts:8880
  #   restart: unless-stopped

volumes:
  models:
    driver: local
  output:
    driver: local

networks:
  default:
    name: kokoro-network
