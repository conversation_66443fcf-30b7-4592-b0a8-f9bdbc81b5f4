# Docker build script for Coqui TTS FastAPI Server
# This script builds the Docker image with proper tagging

param(
    [string]$Tag = "coqui-tts-fastapi:latest",
    [switch]$NoBuildCache,
    [switch]$Push,
    [string]$Registry = "",
    [switch]$Verbose
)

Write-Host "Building Coqui TTS FastAPI Docker Image" -ForegroundColor Green
Write-Host "Tag: $Tag" -ForegroundColor Cyan

# Build arguments
$buildArgs = @("build", "-t", $Tag)

if ($NoBuildCache) {
    $buildArgs += "--no-cache"
    Write-Host "Build cache disabled" -ForegroundColor Yellow
}

if ($Verbose) {
    $buildArgs += "--progress=plain"
}

# Add context
$buildArgs += "."

# Build the image
Write-Host "`nBuilding Docker image..." -ForegroundColor Yellow
Write-Host "Command: docker $($buildArgs -join ' ')" -ForegroundColor Gray

try {
    & docker @buildArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker image built successfully!" -ForegroundColor Green
        
        # Show image info
        Write-Host "`nImage information:" -ForegroundColor Cyan
        & docker images $Tag
        
        # Push to registry if requested
        if ($Push -and $Registry) {
            $fullTag = "$Registry/$Tag"
            Write-Host "`nTagging for registry: $fullTag" -ForegroundColor Yellow
            & docker tag $Tag $fullTag
            
            Write-Host "Pushing to registry..." -ForegroundColor Yellow
            & docker push $fullTag
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Image pushed successfully!" -ForegroundColor Green
            } else {
                Write-Host "✗ Failed to push image" -ForegroundColor Red
            }
        }
        
        # Test the image
        Write-Host "`nTesting the image..." -ForegroundColor Yellow
        Write-Host "You can test the image with:" -ForegroundColor Cyan
        Write-Host "  docker run -p 8881:8881 $Tag" -ForegroundColor Gray
        Write-Host "  docker-compose up" -ForegroundColor Gray
        
    } else {
        Write-Host "✗ Docker build failed!" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "✗ Error during Docker build: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nBuild completed successfully!" -ForegroundColor Green
