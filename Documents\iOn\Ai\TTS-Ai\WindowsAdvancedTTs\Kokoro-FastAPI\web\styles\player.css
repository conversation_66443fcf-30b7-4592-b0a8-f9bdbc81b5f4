.player-container {
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    grid-template-rows: auto auto;
    gap: 0.75rem;
    position: relative;
    overflow: hidden;
    min-width: 0;
    box-sizing: border-box;
    width: 100%;
    padding: clamp(0.75rem, 1.5vw, 1.25rem);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 0.75rem;
    margin-top: clamp(1rem, 2vh, 2rem);
}

@media (max-width: 768px) {
    .player-container {
        padding: 0.75rem;
        gap: 0.75rem;
        margin-top: 1rem;
    }
}

.player-controls {
    display: grid;
    grid-template-columns: auto minmax(100px, 1fr) auto auto auto;
    align-items: center;
    gap: clamp(0.5rem, 1vw, 1rem);
    width: 100%;
    height: 44px;
    padding: 0.5rem 0.5rem;
    border-radius: 0.75rem;
    background: rgba(15, 23, 42, 0.2);
    min-width: 0;
    border: 1px solid var(--border);
}

@media (max-width: 768px) {
    .player-controls {
        grid-template-columns: auto 1fr auto;
        grid-template-rows: auto auto;
        height: auto;
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .volume-control {
        grid-column: 2;
        justify-self: end;
        border-left: none;
        padding-left: 0;
        gap: 0.25rem;
    }

    .time-display {
        grid-column: 3;
        border-left: none;
        padding-left: 0;
        min-width: 55px;
    }

    .seek-slider {
        grid-column: 1 / -1;
        grid-row: 2;
        margin: 0.25rem 0;
    }

    .player-btn {
        padding: 0.25rem 0.5rem;
        min-width: 45px;
        height: 26px;
        font-size: 0.875rem;
    }
}

.seek-slider,
.volume-slider {
    -webkit-appearance: none;
    height: 4px;
    border-radius: 2px;
    background: rgba(99, 102, 241, 0.2);
    outline: none;
    cursor: pointer;
    transition: height 0.2s ease-in-out;
}

.seek-slider {
    width: 100%;
    min-width: 0;
}

.volume-slider {
    width: 80px;
}

.seek-slider::-webkit-slider-thumb,
.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--fg-color);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.seek-slider::-webkit-slider-thumb:hover,
.volume-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
}

.seek-slider::-moz-range-thumb,
.volume-slider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border: none;
    border-radius: 50%;
    background: var(--fg-color);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.seek-slider::-moz-range-thumb:hover,
.volume-slider::-moz-range-thumb:hover {
    transform: scale(1.2);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-left: 0.75rem;
    border-left: 1px solid rgba(99, 102, 241, 0.2);
    min-width: 0;
    box-sizing: border-box;
}

@media (max-width: 768px) {
    .volume-control {
        gap: 0.25rem;
    }

    .volume-slider {
        width: 60px;
    }

    .player-btn {
        padding: 0.375rem 0.75rem;
        min-width: 50px;
        height: 28px;
        font-size: 0.875rem;
    }
}

.volume-icon {
    color: var(--fg-color);
    opacity: 0.8;
    transition: opacity 0.2s ease;
    display: flex;
    align-items: center;
}

.volume-icon:hover {
    opacity: 1;
}

.player-btn {
    background: var(--fg-color);
    color: var(--text);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
}

.player-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}
 
/* Cancel Button Styles */
.player-btn.cancel {
    background: #976161;
    color: #070707;
    border: 1px solid #ffb3b3;
}

.player-btn.cancel:hover {
    background: #ffbaba;
    box-shadow: 0 4px 12px rgba(255, 87, 87, 0.2);
}

.wave-container {
    width: 100%;
    height: 60px;
    background: rgba(15, 23, 42, 0.3);
    border-radius: 0.5rem;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 0;
}

@media (max-width: 768px) {
    .wave-container {
        height: 32px;
    }

    .wave-container canvas {
        height: 100%;
    }

    .download-button {
        bottom: 0.5rem;
        right: 0.5rem;
        width: 26px;
        height: 26px;
    }

    .download-icon {
        width: 26px;
        height: 26px;
    }

    .download-placeholder {
        width: 26px;
        height: 26px;
        margin: 0.25rem;
    }

    .generation-progress {
        height: 3px;
    }
}

/* Generation Controls Responsive Fixes */
@media (max-width: 768px) {
    .button-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    .generation-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .format-select {
        min-width: 80px;
    }

    #generate-btn {
        width: 100%;
        min-height: 40px;
    }
}

/* Ensure all controls stay within bounds */
#text-editor {
    min-width: 0;
    width: 100%;
}

.controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-width: 0;
    width: 100%;
}

.voice-select-container {
    min-width: 0;
    width: 100%;
}

.options {
    min-width: 0;
    width: 100%;
}

.button-group {
    min-width: 0;
    width: 100%;
}

.generation-progress {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 4px;
    margin: 0;
    background: rgba(99, 102, 241, 0.2);
    border: none;
    border-radius: 2px;
    position: absolute;
    bottom: 0;
    left: 0;
}

.generation-progress::-webkit-progress-bar {
    background: rgba(99, 102, 241, 0.2);
    border-radius: 2px;
}

.generation-progress::-webkit-progress-value {
    background: var(--fg-color);
    border-radius: 2px;
    transition: width 0.2s ease;
}

.generation-progress::-moz-progress-bar {
    background: var(--fg-color);
    border-radius: 2px;
    transition: width 0.2s ease;
}

.time-display {
    font-size: 0.875rem;
    color: var(--text-light);
    text-align: right;
    font-variant-numeric: tabular-nums;
    padding-left: 0.75rem;
    border-left: 1px solid rgba(99, 102, 241, 0.2);
    min-width: 70px;
    box-sizing: border-box;
}

@media (max-width: 768px) {
    .time-display {
        font-size: 0.75rem;
        min-width: 60px;
    }
}


.wave-container canvas {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 200%;
    transform: translateY(-50%) scale(0.5);
}

.download-placeholder {
    width: 32px;
    height: 32px;
    margin: 0.75rem;
    visibility: hidden;
}

.download-button {
    position: absolute;
    bottom: 0.75rem;
    right: 0.75rem;
    width: 32px;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
    opacity: 0;
    pointer-events: none;
    z-index: 10;
}

.download-button.ready {
    opacity: 1;
    pointer-events: auto;
}

.download-glow {
    position: absolute;
    inset: -15%;
    background: conic-gradient(
        from 0deg,
        var(--fg-color),
        #6366f1,
        var(--fg-color)
    );
    border-radius: 4px;
    animation: rotate 4s linear infinite;
    filter: blur(8px);
    opacity: 0.5;
}

.download-icon {
    width: 32px;
    height: 32px;
    position: relative;
    z-index: 2;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.download-button:hover {
    transform: scale(1.05);
}

.download-button:hover .download-icon {
    box-shadow: 0 0 15px rgba(34, 197, 94, 0.3);
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
