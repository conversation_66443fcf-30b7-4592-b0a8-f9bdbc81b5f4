# 🎤 Kokoro-FastAPI Integration Guide

This guide shows you how to integrate your new high-quality Kokoro TTS server with your existing UIs.

## 🌐 Server Information

- **API URL**: `http://localhost:8880`
- **OpenAI Compatible**: Yes, fully compatible with OpenAI Speech API
- **Documentation**: `http://localhost:8880/docs`
- **Web Interface**: `http://localhost:8880/web`

## 🔧 Integration Instructions

### 1. **Open WebUI** Integration

Open WebUI has built-in support for OpenAI-compatible TTS services:

1. Go to **Settings** → **Audio**
2. Set **TTS Engine** to "OpenAI"
3. Configure:
   - **API Base URL**: `http://localhost:8880/v1`
   - **API Key**: `not-needed` (or leave empty)
   - **Model**: `kokoro`
   - **Voice**: Choose from available voices (see below)

### 2. **Kobold CPP** Integration

For Kobold CPP, you can use the TTS via API calls or browser extensions:

#### Option A: Browser Extension
- Install a TTS browser extension that supports OpenAI API
- Configure it to use: `http://localhost:8880/v1/audio/speech`

#### Option B: Custom Script
```javascript
// Add this to your Kobold CPP interface
async function speakText(text) {
    const response = await fetch('http://localhost:8880/v1/audio/speech', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            model: 'kokoro',
            input: text,
            voice: 'af_bella',
            response_format: 'mp3'
        })
    });
    
    const audioBlob = await response.blob();
    const audio = new Audio(URL.createObjectURL(audioBlob));
    audio.play();
}
```

### 3. **SillyTavern** Integration

SillyTavern supports OpenAI-compatible TTS:

1. Go to **Extensions** → **TTS**
2. Select **OpenAI** as provider
3. Configure:
   - **API URL**: `http://localhost:8880/v1/audio/speech`
   - **Model**: `kokoro`
   - **Voice**: Select preferred voice

### 4. **General Integration** (Any Application)

For any application that supports OpenAI TTS API:

```bash
# API Endpoint
POST http://localhost:8880/v1/audio/speech

# Headers
Content-Type: application/json

# Body
{
    "model": "kokoro",
    "input": "Your text here",
    "voice": "af_bella",
    "response_format": "mp3",
    "speed": 1.0
}
```

## 🎭 Available Voices

Kokoro-FastAPI includes these high-quality voices:

- **af_bella** - Warm, friendly female voice (recommended)
- **af_sky** - Clear, professional female voice  
- **af_heart** - Expressive, emotional female voice
- **am_adam** - Natural male voice
- **am_michael** - Deep, authoritative male voice

### Voice Mixing (Advanced)
You can also mix voices for unique combinations:
```json
{
    "model": "kokoro",
    "input": "Your text here",
    "voice": "af_bella:0.7,af_sky:0.3",
    "response_format": "mp3"
}
```

## 🎵 Audio Formats

Supported output formats:
- **mp3** (recommended for web)
- **wav** (highest quality)
- **opus** (efficient compression)
- **flac** (lossless compression)

## ⚡ Performance Tips

1. **Speed Control**: Adjust speech speed with `"speed": 0.5` to `2.0`
2. **Batch Processing**: For multiple texts, send separate requests
3. **Caching**: The server caches models for faster subsequent requests
4. **Streaming**: Enable streaming for real-time audio generation

## 🔧 Troubleshooting

### Common Issues:

**Server not responding:**
```bash
# Check if container is running
docker ps

# View logs
docker logs kokoro-tts

# Restart if needed
docker restart kokoro-tts
```

**Audio quality issues:**
- Try different voices
- Adjust speed parameter
- Use WAV format for highest quality

**Integration not working:**
- Verify the API URL is correct
- Check firewall settings
- Ensure the application supports OpenAI API format

## 📝 Example Requests

### PowerShell
```powershell
$body = @{
    model = "kokoro"
    input = "Hello, this is a test of the Kokoro TTS system!"
    voice = "af_bella"
    response_format = "mp3"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:8880/v1/audio/speech" `
    -Method Post -Body $body -Headers @{"Content-Type"="application/json"} `
    -OutFile "output.mp3"
```

### cURL
```bash
curl -X POST "http://localhost:8880/v1/audio/speech" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "kokoro",
    "input": "Hello, this is a test!",
    "voice": "af_bella",
    "response_format": "mp3"
  }' \
  --output output.mp3
```

### Python
```python
import requests

response = requests.post(
    "http://localhost:8880/v1/audio/speech",
    json={
        "model": "kokoro",
        "input": "Hello, this is a test!",
        "voice": "af_bella",
        "response_format": "mp3"
    }
)

with open("output.mp3", "wb") as f:
    f.write(response.content)
```

## 🎯 Next Steps

1. **Test the server** with the provided test script
2. **Configure your UIs** using the instructions above
3. **Experiment with voices** to find your preferred settings
4. **Set up autostart** (optional) to run TTS on system boot

## 📞 Support

If you encounter issues:
1. Check the server logs: `docker logs kokoro-tts`
2. Verify the API is responding: `http://localhost:8880/health`
3. Test with the provided scripts first
4. Check the documentation: `http://localhost:8880/docs`

---

**Enjoy your high-quality, Pi.AI-level TTS experience! 🎉**
