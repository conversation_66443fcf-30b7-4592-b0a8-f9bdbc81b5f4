.badges-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    padding: clamp(0.75rem, 1.5vh, 1rem) clamp(1rem, 2vw, 2rem);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(99, 102, 241, 0.2);
    min-height: clamp(3.5rem, 6vh, 4.5rem);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.badge {
    height: clamp(24px, 3vh, 28px);
    display: flex;
    align-items: center;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: clamp(0.5rem, 1vw, 1rem);
    margin: 0 auto;
    transform: translateX(-50%);
    left: 50%;
    position: absolute;
}

@media (max-width: 768px) {
    .badges-container {
        padding: 0.75rem;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.75rem;
        min-height: clamp(4rem, 8vh, 5rem);
    }
    
    .badge {
        height: 24px;
    }
    
    .badge iframe {
        height: 24px !important;
        max-width: 100%;
    }

    .logo-container {
        position: static;
        transform: none;
        margin: 0;
        order: -1;
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }
}

.badge iframe {
    height: 28px !important;
}

.badge:hover {
    opacity: 0.9;
}

.badge img {
    height: 100%;
    border-radius: 4px;
}
