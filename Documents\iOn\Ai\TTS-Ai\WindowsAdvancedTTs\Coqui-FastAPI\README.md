# Coqui TTS FastAPI Server

OpenAI-compatible API wrapper for Coqui TTS, providing high-quality text-to-speech synthesis with multiple models and voices.

## Features

- **OpenAI-compatible API** - Drop-in replacement for OpenAI TTS API
- **Multiple TTS Models** - Support for various Coqui TTS models including XTTS v2
- **Multi-language Support** - Supports multiple languages through Coqui TTS
- **Voice Cloning** - Advanced voice synthesis capabilities
- **FastAPI Backend** - High-performance async API server
- **Easy Integration** - Simple REST API for any application

## Quick Start

### Prerequisites

- Python 3.11 (via conda environment)
- Coqui TTS installed in conda environment at: `C:\Users\<USER>\miniconda3\envs\coqui-tts`

### Installation

1. Install dependencies:
```powershell
# Using the conda environment
C:\Users\<USER>\miniconda3\envs\coqui-tts\python.exe -m pip install -r requirements.txt
```

2. Start the server:
```powershell
.\start-server.ps1
```

The server will start on `http://localhost:8881`

### Testing

Test the server with the included test script:

```powershell
# Test server status
.\test-tts.ps1 -TestStatus

# Test available models
.\test-tts.ps1 -TestModels

# Test available voices
.\test-tts.ps1 -TestVoices

# Test TTS synthesis
.\test-tts.ps1 -Text "Hello, this is a test" -Voice "default" -Format "wav"
```

## API Endpoints

### OpenAI-Compatible Endpoints

#### POST /v1/audio/speech
Generate speech from text (OpenAI-compatible)

```json
{
  "model": "tts-1",
  "input": "Hello, world!",
  "voice": "default",
  "response_format": "wav",
  "speed": 1.0
}
```

#### GET /v1/models
List available TTS models

### Additional Endpoints

#### GET /v1/voices
List available voices for the current model

#### GET /status
Get server status and configuration

#### GET /
Server health check

## Configuration

### Environment Variables

- `COQUI_TOS_AGREED=1` - Automatically agree to Coqui TOS

### Server Options

- `--host` - Host to bind to (default: 0.0.0.0)
- `--port` - Port to bind to (default: 8881)
- `--reload` - Enable auto-reload for development

## Available Models

The server supports various Coqui TTS models:

- `tts_models/multilingual/multi-dataset/xtts_v2` (default)
- `tts_models/multilingual/multi-dataset/xtts_v1.1`
- `tts_models/multilingual/multi-dataset/your_tts`
- `tts_models/multilingual/multi-dataset/bark`
- And many more language-specific models

## Integration Examples

### Python
```python
import requests

response = requests.post(
    "http://localhost:8881/v1/audio/speech",
    json={
        "model": "tts-1",
        "input": "Hello, world!",
        "voice": "default",
        "response_format": "wav"
    }
)

with open("output.wav", "wb") as f:
    f.write(response.content)
```

### JavaScript
```javascript
const response = await fetch('http://localhost:8881/v1/audio/speech', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        model: 'tts-1',
        input: 'Hello, world!',
        voice: 'default',
        response_format: 'wav'
    })
});

const audioBlob = await response.blob();
const audio = new Audio(URL.createObjectURL(audioBlob));
audio.play();
```

### PowerShell
```powershell
$body = @{
    model = "tts-1"
    input = "Hello, world!"
    voice = "default"
    response_format = "wav"
} | ConvertTo-Json

$response = Invoke-WebRequest -Uri "http://localhost:8881/v1/audio/speech" -Method Post -Body $body -ContentType "application/json"
[System.IO.File]::WriteAllBytes("output.wav", $response.Content)
```

## Docker Support

See the Docker configuration files for containerized deployment.

## Troubleshooting

### Common Issues

1. **Server won't start**: Check that the conda environment is properly activated
2. **Model loading errors**: Ensure sufficient disk space and internet connection for model downloads
3. **Audio quality issues**: Try different models or voice settings

### Logs

The server provides detailed logging for debugging issues.

## License

This project uses Coqui TTS, which has its own licensing terms. Please review the Coqui TTS license before use.
