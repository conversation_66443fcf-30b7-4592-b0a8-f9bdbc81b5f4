# Quick test script for Kokoro TTS
Write-Host "🎤 Testing Kokoro TTS Server..." -ForegroundColor Cyan

# Wait for server to be ready
$maxAttempts = 30
$attempt = 0
$serverReady = $false

Write-Host "⏳ Waiting for server to start..." -ForegroundColor Yellow

do {
    Start-Sleep 2
    $attempt++
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8880/health" -TimeoutSec 5 -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            $serverReady = $true
            Write-Host "✅ Server is ready!" -ForegroundColor Green
            break
        }
    } catch {
        Write-Host "." -NoNewline -ForegroundColor Yellow
    }
} while ($attempt -lt $maxAttempts)

if (-not $serverReady) {
    Write-Host ""
    Write-Host "❌ Server not responding after $maxAttempts attempts" -ForegroundColor Red
    Write-Host "Check if the container is running: docker ps" -ForegroundColor Yellow
    exit 1
}

# Test basic TTS generation
Write-Host ""
Write-Host "🔊 Testing TTS generation..." -ForegroundColor Cyan

try {
    $body = @{
        model = "kokoro"
        input = "Hello! This is a test of the Kokoro TTS system. The quality should be excellent, similar to Pi.AI."
        voice = "af_bella"
        response_format = "mp3"
        speed = 1.0
    } | ConvertTo-Json

    $headers = @{
        "Content-Type" = "application/json"
    }

    Write-Host "Generating audio..." -ForegroundColor White
    $response = Invoke-WebRequest -Uri "http://localhost:8880/v1/audio/speech" -Method Post -Body $body -Headers $headers -OutFile "test_output.mp3"

    if (Test-Path "test_output.mp3") {
        $fileSize = (Get-Item "test_output.mp3").Length
        Write-Host "✅ Audio generated successfully!" -ForegroundColor Green
        Write-Host "   File: test_output.mp3" -ForegroundColor White
        Write-Host "   Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor White
        
        # Try to play the audio
        Write-Host "🎵 Playing audio..." -ForegroundColor Cyan
        Start-Process "test_output.mp3"
        
        Write-Host ""
        Write-Host "🎉 Test completed successfully!" -ForegroundColor Green
        Write-Host "Your Kokoro TTS server is working perfectly!" -ForegroundColor White
        Write-Host ""
        Write-Host "🌐 Access points:" -ForegroundColor Cyan
        Write-Host "   API: http://localhost:8880" -ForegroundColor White
        Write-Host "   Docs: http://localhost:8880/docs" -ForegroundColor White
        Write-Host "   Web UI: http://localhost:8880/web" -ForegroundColor White
    } else {
        Write-Host "❌ Audio file was not created" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error generating audio: $($_.Exception.Message)" -ForegroundColor Red
}
