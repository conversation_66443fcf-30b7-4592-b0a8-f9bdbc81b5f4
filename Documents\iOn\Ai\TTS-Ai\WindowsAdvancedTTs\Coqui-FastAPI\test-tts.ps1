# Test script for Coqui TTS FastAPI Server
# This script tests various TTS functionality

param(
    [string]$Text = "Hello! This is a test of the Coqui TTS system. The quality should be excellent for voice synthesis.",
    [string]$Voice = "default",
    [string]$Format = "wav",
    [string]$OutputFile = "test_output.wav",
    [string]$ServerUrl = "http://localhost:8881",
    [switch]$TestStatus,
    [switch]$TestModels,
    [switch]$TestVoices
)

Write-Host "Coqui TTS FastAPI Server Test Script" -ForegroundColor Green
Write-Host "Server URL: $ServerUrl" -ForegroundColor Cyan

# Test server status
if ($TestStatus) {
    Write-Host "`nTesting server status..." -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri "$ServerUrl/status" -Method Get
        Write-Host "Status: $($response.status)" -ForegroundColor Green
        Write-Host "Current Model: $($response.model)" -ForegroundColor Green
        Write-Host "Available Models: $($response.available_models.Count)" -ForegroundColor Green
        Write-Host "Available Voices: $($response.available_voices -join ', ')" -ForegroundColor Green
    }
    catch {
        Write-Host "Error testing status: $($_.Exception.Message)" -ForegroundColor Red
    }
    return
}

# Test available models
if ($TestModels) {
    Write-Host "`nTesting available models..." -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri "$ServerUrl/v1/models" -Method Get
        Write-Host "Available Models:" -ForegroundColor Green
        foreach ($model in $response.data) {
            Write-Host "  - $($model.id)" -ForegroundColor Cyan
        }
    }
    catch {
        Write-Host "Error testing models: $($_.Exception.Message)" -ForegroundColor Red
    }
    return
}

# Test available voices
if ($TestVoices) {
    Write-Host "`nTesting available voices..." -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri "$ServerUrl/v1/voices" -Method Get
        Write-Host "Available Voices:" -ForegroundColor Green
        foreach ($voice in $response.voices) {
            Write-Host "  - $($voice.id): $($voice.name)" -ForegroundColor Cyan
        }
    }
    catch {
        Write-Host "Error testing voices: $($_.Exception.Message)" -ForegroundColor Red
    }
    return
}

# Test TTS synthesis
Write-Host "`nTesting TTS synthesis..." -ForegroundColor Yellow
Write-Host "Text: $Text" -ForegroundColor Cyan
Write-Host "Voice: $Voice" -ForegroundColor Cyan
Write-Host "Format: $Format" -ForegroundColor Cyan
Write-Host "Output: $OutputFile" -ForegroundColor Cyan

try {
    $body = @{
        model = "tts-1"
        input = $Text
        voice = $Voice
        response_format = $Format
        speed = 1.0
    } | ConvertTo-Json

    Write-Host "`nSending request..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "$ServerUrl/v1/audio/speech" -Method Post -Body $body -ContentType "application/json"
    
    if ($response.StatusCode -eq 200) {
        # Save the audio file
        [System.IO.File]::WriteAllBytes($OutputFile, $response.Content)
        Write-Host "✓ TTS synthesis successful!" -ForegroundColor Green
        Write-Host "Audio saved to: $OutputFile" -ForegroundColor Green
        Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1KB, 2)) KB" -ForegroundColor Green
        
        # Try to play the audio (if possible)
        if (Get-Command "Start-Process" -ErrorAction SilentlyContinue) {
            Write-Host "`nAttempting to play audio..." -ForegroundColor Yellow
            try {
                Start-Process $OutputFile
            }
            catch {
                Write-Host "Could not auto-play audio. Please play manually: $OutputFile" -ForegroundColor Yellow
            }
        }
    }
    else {
        Write-Host "✗ TTS synthesis failed with status: $($response.StatusCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ Error during TTS synthesis: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error details: $errorBody" -ForegroundColor Red
    }
}

Write-Host "`nTest completed." -ForegroundColor Green
