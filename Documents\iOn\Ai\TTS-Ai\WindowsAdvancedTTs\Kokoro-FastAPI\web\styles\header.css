.logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

h1 {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
    background: linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 5px 5px;
    -webkit-background-clip: text;
    background-clip: text;
    color: var(--text);
    text-shadow: 
        -1px -1px 0 rgba(0,0,0,0.5),  
        1px -1px 0 rgba(0,0,0,0.5),
        -1px 1px 0 rgba(0,0,0,0.5),
        1px 1px 0 rgba(0,0,0,0.5),
        2px 2px var(--fg-color);
}

@media (max-width: 768px) {
    .logo-container {
        gap: 0.5rem;
    }
    
    h1 {
        font-size: 1.5rem;
    }
}

.cup {
    width: 16px;
    height: 20px;
    border: 2px solid var(--text);
    border-radius: 0 0 8px 8px;
    position: relative;
    animation: float 3s ease-in-out;
    animation-iteration-count: 3;
    animation-fill-mode: forwards;
}

.handle {
    width: 6px;
    height: 10px;
    border: 2px solid var(--text);
    border-radius: 0 4px 4px 0;
    position: absolute;
    right: -6px;
    top: 4px;
}

.steam {
    position: absolute;
    top: -6px;
    left: 2px;
    right: 2px;
    height: 6px;
    display: flex;
    justify-content: space-between;
}

.steam::before,
.steam::after {
    content: "";
    width: 3px;
    height: 100%;
    background: rgba(255,255,255,0.7);
    border-radius: 3px;
    animation: steam 2s;
    animation-iteration-count: 3;
    animation-fill-mode: forwards;
}

@keyframes steam {
    to {
        transform: translateY(-6px) scale(1.3);
        opacity: 0;
    }
}

@keyframes float {
    50% {
        transform: translateY(-2px);
    }
    100% {
        transform: translateY(0);
    }
}
