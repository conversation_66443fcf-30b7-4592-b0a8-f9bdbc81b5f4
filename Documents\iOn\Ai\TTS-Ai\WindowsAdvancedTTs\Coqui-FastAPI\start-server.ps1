# Coqui TTS FastAPI Server Startup Script
# This script starts the Coqui TTS server using the conda environment

param(
    [string]$Host = "0.0.0.0",
    [int]$Port = 8881,
    [switch]$Reload
)

Write-Host "Starting Coqui TTS FastAPI Server..." -ForegroundColor Green
Write-Host "Host: $Host" -ForegroundColor Cyan
Write-Host "Port: $Port" -ForegroundColor Cyan

# Set environment variables
$env:COQUI_TOS_AGREED = "1"

# Build the command
$pythonPath = "C:\Users\<USER>\miniconda3\envs\coqui-tts\python.exe"
$args = @("main.py", "--host", $Host, "--port", $Port)

if ($Reload) {
    $args += "--reload"
    Write-Host "Auto-reload enabled" -ForegroundColor Yellow
}

# Start the server
Write-Host "Command: $pythonPath $($args -join ' ')" -ForegroundColor Gray
& $pythonPath @args
