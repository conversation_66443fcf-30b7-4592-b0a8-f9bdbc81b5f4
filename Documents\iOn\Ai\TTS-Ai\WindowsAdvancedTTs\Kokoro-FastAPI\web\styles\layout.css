/* Main Layout */
main {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 1rem;
    width: 80%;
    margin: 0 auto;
    min-width: 0;
    height: calc(100vh - 8rem);
}

/* Main Column */
.main-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: min-content;
    height: auto;
    overflow-y: auto;
}

/* Text Editor Container */
.text-editor {
    min-height: 400px;
    height: auto;
    overflow: auto;
    background: rgba(15, 23, 42, 0.3);
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    padding: 0.75rem;
}

/* Controls Panel */
.controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(99, 102, 241, 0.2) transparent;
}

.controls::-webkit-scrollbar {
    width: 6px;
}

.controls::-webkit-scrollbar-track {
    background: transparent;
}

.controls::-webkit-scrollbar-thumb {
    background-color: rgba(99, 102, 241, 0.2);
    border-radius: 3px;
}

/* Controls Sections */
.voice-select-container,
.speed-control,
.button-group {
    width: 100%;
    background: rgba(15, 23, 42, 0.3);
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    padding: 0.75rem;
}

/* Player Container */
.player-container {
    background: rgba(15, 23, 42, 0.3);
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    padding: 0.75rem;
}

/* Responsive Layout */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        width: 95%;
        height: auto;
    }

    .text-editor {
        min-height: 300px;
    }

    .controls {
        max-height: none;
        overflow: visible;
    }
}
