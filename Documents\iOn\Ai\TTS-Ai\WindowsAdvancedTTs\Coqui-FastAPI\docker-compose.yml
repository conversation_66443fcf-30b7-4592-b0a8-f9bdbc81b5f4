version: '3.8'

services:
  coqui-tts:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: coqui-tts-server
    ports:
      - "8881:8881"
    environment:
      - COQUI_TOS_AGREED=1
      - PYTHONUNBUFFERED=1
    volumes:
      # Mount for model cache (optional, for persistence)
      - coqui_models:/app/models
      - coqui_cache:/app/cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8881/').raise_for_status()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  # Optional: GPU-enabled version (uncomment if you have NVIDIA GPU support)
  # coqui-tts-gpu:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.gpu
  #   container_name: coqui-tts-gpu-server
  #   ports:
  #     - "8881:8881"
  #   environment:
  #     - COQUI_TOS_AGREED=1
  #     - PYTHONUNBUFFERED=1
  #     - CUDA_VISIBLE_DEVICES=0
  #   volumes:
  #     - coqui_models:/app/models
  #     - coqui_cache:/app/cache
  #   restart: unless-stopped
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             count: 1
  #             capabilities: [gpu]

volumes:
  coqui_models:
    driver: local
  coqui_cache:
    driver: local

networks:
  default:
    name: coqui-tts-network
