/* Text Editor */
.text-editor {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 0.75rem;
    padding: clamp(0.75rem, 2vw, 1.25rem);
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
}

.editor-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-height: 0;
}

.page-content {
    flex: 1;
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    background: rgba(15, 23, 42, 0.3);
    color: var(--text);
    font-family: var(--font-family);
    font-size: 1rem;
    resize: vertical;
    transition: border-color 0.2s ease;
    min-height: 300px;
    box-sizing: border-box;
    min-width: 0;
    white-space: pre-wrap;
    word-break: break-word;
}

.page-content:focus {
    outline: none;
    border-color: var(--fg-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.page-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem;
    border-bottom: 1px solid var(--border);
    margin-bottom: 0.5rem;
    background: rgba(15, 23, 42, 0.3);
    border-radius: 0.5rem 0.5rem 0 0;
}

.page-navigation .pagination {
    transform: scale(0.9);
}

.page-navigation .pagination button {
    padding: 0.25rem 0.75rem;
}

.editor-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
}

.chars-per-page {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.format-btn {
    background: transparent;
    border: 1px solid var(--border);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;  
    transition: all 0.2s ease;
    width: auto;
    font-size: 0.875rem;
}

.format-btn:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--fg-color);
    transform: none;
    box-shadow: none;
}

.chars-input {
    width: 70px;
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border);
    border-radius: 0.25rem;
    background: rgba(15, 23, 42, 0.3);
    color: var(--text);
    font-size: 0.875rem;
    text-align: center;
}

.chars-input:focus {
    outline: none;
    border-color: var(--fg-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.chars-label {
    color: var(--text-light);
    font-size: 0.875rem;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.pagination button {
    background: transparent;
    border: 1px solid var(--border);
    color: var(--text);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    width: auto;
}

.pagination button:hover:not(:disabled) {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--fg-color);
    transform: none;
    box-shadow: none;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    color: var(--text-light);
    font-size: 0.875rem;
    min-width: 100px;
    text-align: center;
}

.char-count {
    color: var(--text-light);
    font-size: 0.875rem;
    min-width: 100px;
    text-align: right;
}

.file-controls {
    display: flex;
    gap: 0.5rem;
}

.upload-btn,
.clear-btn {
    background: transparent;
    border: 1px solid var(--border);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    width: auto;
    font-size: 0.875rem;
}

.upload-btn:hover,
.clear-btn:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--fg-color);
    transform: none;
    box-shadow: none;
}

/* Scrollbar Styles */
.text-editor {
    scrollbar-width: thin;
    scrollbar-color: rgba(99, 102, 241, 0.2) transparent;
}

.text-editor::-webkit-scrollbar {
    width: 6px;
}

.text-editor::-webkit-scrollbar-thumb {
    background: rgba(99, 102, 241, 0.2);
    border-radius: 3px;
}

.text-editor::-webkit-scrollbar-track {
    background: transparent;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .editor-footer {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chars-per-page {
        width: 100%;
        justify-content: space-between;
    }

    .file-controls {
        width: 100%;
        justify-content: space-between;
    }

    .upload-btn,
    .clear-btn {
        flex: 1;
        text-align: center;
    }
}
