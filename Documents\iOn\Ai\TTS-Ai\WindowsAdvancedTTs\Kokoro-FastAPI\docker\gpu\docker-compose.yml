name: kokoro-tts-gpu
services:
  kokoro-tts:
    # image: ghcr.io/remsky/kokoro-fastapi-gpu:v${VERSION}
    build:
      context: ../..
      dockerfile: docker/gpu/Dockerfile
    volumes:
      - ../../api:/app/api
    user: "1001:1001"  # Ensure container runs as UID 1001 (appuser)
    ports:
      - "8880:8880"
    environment:
      - PYTHONPATH=/app:/app/api
      - USE_GPU=true
      - PYTHONUNBUFFERED=1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  # # Gradio UI service
  # gradio-ui:
  #   image: ghcr.io/remsky/kokoro-fastapi-ui:v${VERSION}
  #   # Uncomment below to build from source instead of using the released image
  #   # build:
  #     # context: ../../ui
  #   ports:
  #     - "7860:7860"
  #   volumes:
  #     - ../../ui/data:/app/ui/data
  #     - ../../ui/app.py:/app/app.py  # Mount app.py for hot reload
  #   environment:
  #     - GRADIO_WATCH=1  # Enable hot reloading
  #     - PYTHONUNBUFFERED=1  # Ensure Python output is not buffered
  #     - DISABLE_LOCAL_SAVING=false  # Set to 'true' to disable local saving and hide file view
  #     - API_HOST=kokoro-tts  # Set TTS service URL
  #     - API_PORT=8880  # Set TTS service PORT
