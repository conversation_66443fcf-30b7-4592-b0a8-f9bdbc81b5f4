name: CI
on:
  push:
    branches: [ "master", "pre-release" ]
  pull_request:
    branches: [ "master", "pre-release" ]
jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10"]
      fail-fast: false
    
    steps:
    - uses: actions/checkout@v4
    
    # Match Dockerfile dependencies
    - name: Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y --no-install-recommends \
          espeak-ng \
          git \
          libsndfile1 \
          curl \
          ffmpeg
    
    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        python-version: ${{ matrix.python-version }}
        enable-cache: true
    - name: Install dependencies
      run: |
        uv pip install -e .[test,cpu]
    - name: Run Tests
      run: |
        uv run pytest api/tests/ --asyncio-mode=auto --cov=api --cov-report=term-missing
