# This file was autogenerated by uv via the following command:
#    uv pip compile docs/requirements.in --universal --output-file docs/requirements.txt
aiofiles==23.2.1
    # via -r docs/requirements.in
annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via
    #   httpx
    #   starlette
attrs==24.3.0
    # via
    #   clldutils
    #   csvw
    #   jsonschema
    #   phonemizer
    #   referencing
babel==2.16.0
    # via csvw
certifi==2024.12.14
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via soundfile
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via uvicorn
clldutils==3.21.0
    # via segments
colorama==0.4.6
    # via
    #   click
    #   colorlog
    #   csvw
    #   loguru
    #   pytest
    #   tqdm
coloredlogs==15.0.1
    # via onnxruntime
colorlog==6.9.0
    # via clldutils
csvw==3.5.1
    # via segments
dlinfo==1.2.1
    # via phonemizer
exceptiongroup==1.2.2 ; python_full_version < '3.11'
    # via
    #   anyio
    #   pytest
fastapi==0.115.6
    # via -r docs/requirements.in
filelock==3.16.1
    # via
    #   huggingface-hub
    #   transformers
flatbuffers==24.12.23
    # via onnxruntime
fsspec==2024.12.0
    # via huggingface-hub
greenlet==3.1.1 ; platform_machine == 'AMD64' or platform_machine == 'WIN32' or platform_machine == 'aarch64' or platform_machine == 'amd64' or platform_machine == 'ppc64le' or platform_machine == 'win32' or platform_machine == 'x86_64'
    # via sqlalchemy
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.26.0
    # via -r docs/requirements.in
huggingface-hub==0.27.1
    # via
    #   tokenizers
    #   transformers
humanfriendly==10.0
    # via coloredlogs
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
iniconfig==2.0.0
    # via pytest
isodate==0.7.2
    # via
    #   csvw
    #   rdflib
joblib==1.4.2
    # via phonemizer
jsonschema==4.23.0
    # via csvw
jsonschema-specifications==2024.10.1
    # via jsonschema
language-tags==1.2.0
    # via csvw
loguru==0.7.3
    # via -r docs/requirements.in
lxml==5.3.0
    # via clldutils
markdown==3.7
    # via clldutils
markupsafe==3.0.2
    # via clldutils
mpmath==1.3.0
    # via sympy
munch==4.0.0
    # via -r docs/requirements.in
numpy==2.2.1
    # via
    #   -r docs/requirements.in
    #   onnxruntime
    #   scipy
    #   soundfile
    #   transformers
onnxruntime==1.20.1
    # via -r docs/requirements.in
packaging==24.2
    # via
    #   huggingface-hub
    #   onnxruntime
    #   pytest
    #   transformers
phonemizer==3.3.0
    # via -r docs/requirements.in
pluggy==1.5.0
    # via pytest
protobuf==5.29.3
    # via onnxruntime
pycparser==2.22
    # via cffi
pydantic==2.10.4
    # via
    #   -r docs/requirements.in
    #   fastapi
    #   pydantic-settings
pydantic-core==2.27.2
    # via pydantic
pydantic-settings==2.7.0
    # via -r docs/requirements.in
pylatexenc==2.10
    # via clldutils
pyparsing==3.2.1
    # via rdflib
pyreadline3==3.5.4 ; sys_platform == 'win32'
    # via humanfriendly
pytest==8.0.0
    # via
    #   -r docs/requirements.in
    #   pytest-asyncio
pytest-asyncio==0.23.5
    # via -r docs/requirements.in
python-dateutil==2.9.0.post0
    # via
    #   clldutils
    #   csvw
python-dotenv==1.0.1
    # via
    #   -r docs/requirements.in
    #   pydantic-settings
pyyaml==6.0.2
    # via
    #   huggingface-hub
    #   transformers
rdflib==7.1.2
    # via csvw
referencing==0.35.1
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   -r docs/requirements.in
    #   segments
    #   tiktoken
    #   transformers
requests==2.32.3
    # via
    #   -r docs/requirements.in
    #   csvw
    #   huggingface-hub
    #   tiktoken
    #   transformers
rfc3986==1.5.0
    # via csvw
rpds-py==0.22.3
    # via
    #   jsonschema
    #   referencing
safetensors==0.5.2
    # via transformers
scipy==1.14.1
    # via -r docs/requirements.in
segments==2.2.1
    # via phonemizer
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   httpx
soundfile==0.13.0
    # via -r docs/requirements.in
sqlalchemy==2.0.27
    # via -r docs/requirements.in
starlette==0.41.3
    # via fastapi
sympy==1.13.3
    # via onnxruntime
tabulate==0.9.0
    # via clldutils
tiktoken==0.8.0
    # via -r docs/requirements.in
tokenizers==0.21.0
    # via transformers
tomli==2.2.1 ; python_full_version < '3.11'
    # via pytest
tqdm==4.67.1
    # via
    #   -r docs/requirements.in
    #   huggingface-hub
    #   transformers
transformers==4.47.1
    # via -r docs/requirements.in
typing-extensions==4.12.2
    # via
    #   anyio
    #   fastapi
    #   huggingface-hub
    #   phonemizer
    #   pydantic
    #   pydantic-core
    #   sqlalchemy
    #   uvicorn
uritemplate==4.1.1
    # via csvw
urllib3==2.3.0
    # via requests
uvicorn==0.34.0
    # via -r docs/requirements.in
win32-setctime==1.2.0 ; sys_platform == 'win32'
    # via loguru
