# Test script for Kokoro-FastAPI TTS Server
# This script tests various TTS functionality

param(
    [string]$Text = "Hello! This is a test of the Kokoro TTS system. The quality should be excellent, similar to Pi.AI.",
    [string]$Voice = "af_bella",
    [string]$Format = "mp3",
    [string]$OutputFile = "test_output.mp3",
    [switch]$TestAllVoices,
    [switch]$TestFormats,
    [switch]$TestStreaming
)

$baseUrl = "http://localhost:8880"
$apiUrl = "$baseUrl/v1"

Write-Host "🎤 Testing Kokoro-FastAPI TTS Server" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Check if server is running
try {
    $healthCheck = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Server is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Server is not responding. Please start the server first." -ForegroundColor Red
    Write-Host "   Run: .\start-optimized.ps1" -ForegroundColor Yellow
    exit 1
}

# Test 1: Basic TTS Generation
Write-Host "`n🔊 Test 1: Basic TTS Generation" -ForegroundColor Yellow
try {
    $body = @{
        model = "kokoro"
        input = $Text
        voice = $Voice
        response_format = $Format
        speed = 1.0
    } | ConvertTo-Json

    $headers = @{
        "Content-Type" = "application/json"
    }

    Write-Host "Generating audio with voice: $Voice" -ForegroundColor White
    $response = Invoke-WebRequest -Uri "$apiUrl/audio/speech" -Method Post -Body $body -Headers $headers -OutFile $OutputFile

    if (Test-Path $OutputFile) {
        $fileSize = (Get-Item $OutputFile).Length
        Write-Host "✅ Audio generated successfully!" -ForegroundColor Green
        Write-Host "   File: $OutputFile" -ForegroundColor White
        Write-Host "   Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor White
        
        # Try to play the audio (if possible)
        if (Get-Command "Start-Process" -ErrorAction SilentlyContinue) {
            Write-Host "🎵 Playing audio..." -ForegroundColor Cyan
            Start-Process $OutputFile
        }
    } else {
        Write-Host "❌ Audio file was not created" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error generating audio: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: List Available Voices
Write-Host "`n🎭 Test 2: Available Voices" -ForegroundColor Yellow
try {
    $voices = Invoke-RestMethod -Uri "$apiUrl/audio/voices" -Method Get
    Write-Host "✅ Available voices:" -ForegroundColor Green
    foreach ($voice in $voices.voices) {
        Write-Host "   - $voice" -ForegroundColor White
    }
} catch {
    Write-Host "❌ Error fetching voices: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test All Voices (if requested)
if ($TestAllVoices) {
    Write-Host "`n🎭 Test 3: Testing All Voices" -ForegroundColor Yellow
    try {
        $voices = Invoke-RestMethod -Uri "$apiUrl/audio/voices" -Method Get
        $testText = "Hello, this is a voice test."
        
        foreach ($voiceName in $voices.voices) {
            Write-Host "Testing voice: $voiceName" -ForegroundColor White
            
            $body = @{
                model = "kokoro"
                input = $testText
                voice = $voiceName
                response_format = "mp3"
            } | ConvertTo-Json

            $outputFile = "test_voice_$voiceName.mp3"
            $response = Invoke-WebRequest -Uri "$apiUrl/audio/speech" -Method Post -Body $body -Headers $headers -OutFile $outputFile
            
            if (Test-Path $outputFile) {
                Write-Host "   ✅ $voiceName - Generated successfully" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $voiceName - Failed" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "❌ Error testing voices: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 4: Test Different Formats (if requested)
if ($TestFormats) {
    Write-Host "`n🎵 Test 4: Testing Audio Formats" -ForegroundColor Yellow
    $formats = @("mp3", "wav", "opus", "flac")
    $testText = "Testing different audio formats."
    
    foreach ($fmt in $formats) {
        Write-Host "Testing format: $fmt" -ForegroundColor White
        
        try {
            $body = @{
                model = "kokoro"
                input = $testText
                voice = $Voice
                response_format = $fmt
            } | ConvertTo-Json

            $outputFile = "test_format.$fmt"
            $response = Invoke-WebRequest -Uri "$apiUrl/audio/speech" -Method Post -Body $body -Headers $headers -OutFile $outputFile
            
            if (Test-Path $outputFile) {
                $fileSize = (Get-Item $outputFile).Length
                Write-Host "   ✅ $fmt - $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $fmt - Failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "   ❌ $fmt - Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Test 5: Performance Test
Write-Host "`n⚡ Test 5: Performance Test" -ForegroundColor Yellow
try {
    $longText = "This is a longer text to test the performance of the TTS system. " * 10
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    $body = @{
        model = "kokoro"
        input = $longText
        voice = $Voice
        response_format = "mp3"
    } | ConvertTo-Json

    $response = Invoke-WebRequest -Uri "$apiUrl/audio/speech" -Method Post -Body $body -Headers $headers -OutFile "performance_test.mp3"
    
    $stopwatch.Stop()
    
    if (Test-Path "performance_test.mp3") {
        $fileSize = (Get-Item "performance_test.mp3").Length
        Write-Host "✅ Performance test completed!" -ForegroundColor Green
        Write-Host "   Time: $($stopwatch.ElapsedMilliseconds) ms" -ForegroundColor White
        Write-Host "   Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor White
        Write-Host "   Text length: $($longText.Length) characters" -ForegroundColor White
    }
} catch {
    Write-Host "❌ Performance test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Testing completed!" -ForegroundColor Green
Write-Host "Check the generated audio files to verify quality." -ForegroundColor White
