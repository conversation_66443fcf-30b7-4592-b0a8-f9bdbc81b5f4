# Default values for kokoro-fastapi.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
kokoroTTS:
  replicaCount: 1
  # The name of the deployment repository
  repository: "ghcr.io/remsky/kokoro-fastapi-gpu"
  imagePullSecrets: [] # Set if using a private image or getting rate limited
  tag: "latest"
  pullPolicy: Always
  port: 8880
  resources:
    limits:
      nvidia.com/gpu: 1
    requests:
      nvidia.com/gpu: 1

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP

ingress:
  enabled: false
  className: "nginx"
  annotations: {}
    # cert-manager.io/cluster-issuer: letsencrypt-prod
    # external-dns.alpha.kubernetes.io/hostname: kokoro.example.com
    # external-dns.alpha.kubernetes.io/cloudflare-proxied: "false"
  hosts:
    - host: kokoro.example.com
      paths:
        - path: /
          pathType: Prefix

  tls: []
  #  - secretName: kokoro-fastapi-tls
  #    hosts:
  #      - kokoro.example.com

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
