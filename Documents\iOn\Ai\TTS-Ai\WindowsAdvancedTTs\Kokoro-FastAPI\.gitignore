# Version control
.git

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.py[cod]
*$py.class
.Python
.pytest_cache
.coverage
.coveragerc

# Python package build artifacts
*.egg-info/
*.egg
dist/
build/
*.onnx
*.pth
# Environment
# .env
.venv/
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Project specific
# Model files

*.pth
*.tar*


# Other project files
.env
Kokoro-82M/
ui/data/
EXTERNAL_UV_DOCUMENTATION*
app
api/temp_files/

# Docker
Dockerfile*
docker-compose*
examples/ebook_test/chapter_to_audio.py
examples/ebook_test/chapters_to_audio.py
examples/ebook_test/parse_epub.py
api/src/voices/af_jadzia.pt
examples/assorted_checks/test_combinations/output/*
examples/assorted_checks/test_openai/output/*


# Audio files
examples/*.wav
examples/*.pcm
examples/*.mp3
examples/*.flac
examples/*.acc
examples/*.ogg
examples/speech.mp3
examples/phoneme_examples/output/*.wav
examples/assorted_checks/benchmarks/output_audio/*
uv.lock

# Mac MPS virtualenv for dual testing
.venv-mps
