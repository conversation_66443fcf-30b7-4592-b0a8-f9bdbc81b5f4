:root {
    --bg-color: #0f172a;
    --fg-color: #6366f1;
    --surface: rgba(30, 41, 59, 1);
    --text: #f8fafc;
    --text-light: #cbd5e1;
    --border: rgba(148, 163, 184, 0.2);
    --error: #ef4444;
    --success: #22c55e;
    --font-family: 'Inter', system-ui, sans-serif;
}

html {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text);
    background: var(--bg-color);
    min-height: 100vh;
    position: relative;
    padding: 0;
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
}

.overlay {
    position: fixed;
    inset: 0;
    background:
        radial-gradient(circle at top right,
            var(--fg-color) 0%,
            var(--bg-color) 100%);
    pointer-events: none;
    z-index: 0;
}

.grid-overlay {
    position: fixed;
    inset: 0;
    background-image:
        repeating-linear-gradient(0deg,
            rgba(255,255,255,0.03) 0px,
            rgba(255,255,255,0.03) 1px,
            transparent 1px,
            transparent 20px),
        repeating-linear-gradient(90deg,
            rgba(255,255,255,0.03) 0px,
            rgba(255,255,255,0.03) 1px,
            transparent 1px,
            transparent 20px);
    pointer-events: none;
    z-index: 0;
}

.container {
    width: 100%;
    max-width: min(1400px, 98vw);
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: clamp(5rem, 8vh, 7rem) clamp(0.75rem, 2vw, 2rem) 2rem;
    min-height: 100vh;
}

@media (max-width: 768px) {
    .container {
        padding-top: clamp(6rem, 10vh, 8rem);
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

main {
    display: flex;
    flex-direction: column;
    gap: clamp(1rem, 2vh, 2rem);
    min-width: 0;
    width: 100%;
    position: relative;
    flex: 1;
}

.status {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    opacity: 0;
    font-weight: 500;
    text-align: center;
}

.status.info {
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.2);
    opacity: 1;
}

.status.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    opacity: 1;
}

.status.success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    opacity: 1;
}
