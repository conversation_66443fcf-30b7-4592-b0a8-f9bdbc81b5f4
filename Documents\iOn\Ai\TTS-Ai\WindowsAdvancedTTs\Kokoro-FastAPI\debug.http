### Get Thread Information
GET http://localhost:8880/debug/threads
Accept: application/json

### Get Storage Information
GET http://localhost:8880/debug/storage
Accept: application/json

### Get System Information
GET http://localhost:8880/debug/system
Accept: application/json

### Get Session Pool Status
# Shows active ONNX sessions, CUDA stream usage, and session ages
# Useful for debugging resource exhaustion issues
GET http://localhost:8880/debug/session_pools
Accept: application/json

### List Available Models
# Returns list of all available models in OpenAI format
# Response includes tts-1, tts-1-hd, and kokoro models
GET http://localhost:8880/v1/models
Accept: application/json

### Get Specific Model
# Returns same model list as above for compatibility
# Works with any model name (e.g., tts-1, tts-1-hd, kokoro)
GET http://localhost:8880/v1/models/tts-1
Accept: application/json