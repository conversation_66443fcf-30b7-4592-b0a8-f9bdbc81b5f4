# Coqui TTS FastAPI Server Docker Image
# Multi-stage build for optimized image size

# Build stage
FROM python:3.11-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    git \
    espeak-ng \
    libsndfile1-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Runtime stage
FROM python:3.11-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    espeak-ng \
    libsndfile1 \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd --create-home --shell /bin/bash app

# Set working directory
WORKDIR /app

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code
COPY main.py .
COPY requirements.txt .

# Set environment variables
ENV PYTHONPATH=/app
ENV COQUI_TOS_AGREED=1
ENV PYTHONUNBUFFERED=1

# Create directories for models and cache
RUN mkdir -p /app/models /app/cache && \
    chown -R app:app /app

# Switch to non-root user
USER app

# Expose port
EXPOSE 8881

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8881/').raise_for_status()" || exit 1

# Default command
CMD ["python", "main.py", "--host", "0.0.0.0", "--port", "8881"]
