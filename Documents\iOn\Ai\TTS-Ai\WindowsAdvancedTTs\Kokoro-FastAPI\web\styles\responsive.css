@media (max-width: 1200px) {
    .container {
        max-width: 100%;
    }

    main {
        gap: 1rem;
    }

    .text-editor,
    .controls {
        padding: 1rem;
    }
}

@media (max-width: 1023px) {
    h1 {
        font-size: clamp(1.5rem, 4vw, 2rem);
    }

    .cup {
        width: clamp(20px, 3vw, 30px);
        height: clamp(25px, 4vw, 40px);
    }

    .handle {
        width: clamp(8px, 1.5vw, 12px);
        height: clamp(15px, 2.5vw, 20px);
        right: clamp(-8px, -1.5vw, -12px);
        top: clamp(6px, 1vw, 8px);
    }

    .steam {
        top: clamp(-8px, -1.5vw, -12px);
    }

    .steam::before,
    .steam::after {
        width: clamp(4px, 0.75vw, 6px);
    }
}

@media (max-width: 768px) {
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .text-editor,
    .controls {
        padding: 0.75rem;
    }

    .voice-select-container {
        flex-direction: column;
        align-items: stretch;
    }

    .options {
        flex-direction: column;
        gap: 0.75rem;
    }

    .button-group {
        flex-direction: column;
    }

    .generation-options {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .format-select {
        width: 100%;
    }

    .player-container {
        padding: 0.75rem;
    }

    .player-controls {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .volume-control {
        gap: 0.25rem;
    }

    .volume-slider {
        width: 60px;
    }

    .wave-container {
        height: 32px;
    }

    .download-button {
        top: 0.5rem;
        right: 0.5rem;
        width: 26px;
        height: 26px;
    }

    .download-icon {
        width: 26px;
        height: 26px;
    }
}
