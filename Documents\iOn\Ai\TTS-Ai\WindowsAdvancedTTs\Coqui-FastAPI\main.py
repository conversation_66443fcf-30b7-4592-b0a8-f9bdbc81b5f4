#!/usr/bin/env python3
"""
Coqui TTS FastAPI Server
OpenAI-compatible API wrapper for Coqui TTS
"""

import os
import io
import time
import tempfile
from typing import Optional, List, Dict, Any
from pathlib import Path

import uvicorn
from fastapi import FastAPI, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Import TTS after setting environment
os.environ["COQUI_TOS_AGREED"] = "1"  # Agree to TOS automatically
from TTS.api import TTS


class OpenAISpeechRequest(BaseModel):
    """OpenAI-compatible speech request"""
    model: str = Field(default="tts-1", description="TTS model to use")
    input: str = Field(..., description="Text to synthesize")
    voice: str = Field(default="default", description="Voice to use")
    response_format: str = Field(default="mp3", description="Audio format")
    speed: float = Field(default=1.0, ge=0.25, le=4.0, description="Speech speed")


class TTSStatus(BaseModel):
    """TTS service status"""
    status: str
    model: str
    available_models: List[str]
    available_voices: List[str]


class CoquiTTSService:
    """Coqui TTS service wrapper"""
    
    def __init__(self):
        self.tts = None
        self.current_model = None
        self.available_models = []
        self.available_voices = ["default"]
        self.initialize()
    
    def initialize(self):
        """Initialize TTS service"""
        try:
            # Get available models using ModelManager
            from TTS.utils.manage import ModelManager
            model_manager = ModelManager()
            self.available_models = model_manager.list_models()

            # Initialize with a default model (XTTS v2 for multilingual support)
            default_model = "tts_models/multilingual/multi-dataset/xtts_v2"
            if default_model in self.available_models:
                self.load_model(default_model)
            else:
                # Fallback to first available model
                if self.available_models:
                    self.load_model(self.available_models[0])

        except Exception as e:
            print(f"Error initializing TTS service: {e}")
            self.available_models = []
    
    def load_model(self, model_name: str):
        """Load a specific TTS model"""
        try:
            if self.current_model != model_name:
                print(f"Loading model: {model_name}")
                self.tts = TTS(model_name=model_name)
                self.current_model = model_name
                
                # Update available voices based on model
                if hasattr(self.tts, 'speakers') and self.tts.speakers:
                    self.available_voices = self.tts.speakers
                else:
                    self.available_voices = ["default"]
                    
        except Exception as e:
            print(f"Error loading model {model_name}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to load model: {e}")
    
    def synthesize(self, text: str, voice: str = "default", speed: float = 1.0) -> bytes:
        """Synthesize speech from text"""
        if not self.tts:
            raise HTTPException(status_code=500, detail="TTS service not initialized")
        
        try:
            # Create temporary file for output
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            # Synthesize speech
            if hasattr(self.tts, 'speakers') and self.tts.speakers and voice in self.tts.speakers:
                # Multi-speaker model
                self.tts.tts_to_file(text=text, speaker=voice, file_path=tmp_path)
            else:
                # Single speaker model
                self.tts.tts_to_file(text=text, file_path=tmp_path)
            
            # Read the generated audio file
            with open(tmp_path, "rb") as f:
                audio_data = f.read()
            
            # Clean up temporary file
            os.unlink(tmp_path)
            
            return audio_data
            
        except Exception as e:
            print(f"Error synthesizing speech: {e}")
            raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {e}")


# Initialize FastAPI app
app = FastAPI(
    title="Coqui TTS FastAPI Server",
    description="OpenAI-compatible API for Coqui TTS",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize TTS service
tts_service = CoquiTTSService()


@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Coqui TTS FastAPI Server", "status": "running"}


@app.get("/v1/models")
async def list_models():
    """List available TTS models (OpenAI-compatible)"""
    models = []
    for model_name in tts_service.available_models[:10]:  # Limit to first 10 for performance
        models.append({
            "id": model_name,
            "object": "model",
            "created": int(time.time()),
            "owned_by": "coqui"
        })
    
    return {"object": "list", "data": models}


@app.get("/v1/voices")
async def list_voices():
    """List available voices"""
    return {
        "voices": [
            {"id": voice, "name": voice} 
            for voice in tts_service.available_voices
        ]
    }


@app.get("/status")
async def get_status():
    """Get TTS service status"""
    return TTSStatus(
        status="ready" if tts_service.tts else "not_ready",
        model=tts_service.current_model or "none",
        available_models=tts_service.available_models[:10],  # Limit for performance
        available_voices=tts_service.available_voices
    )


@app.post("/v1/audio/speech")
async def create_speech(request: OpenAISpeechRequest):
    """Generate speech from text (OpenAI-compatible)"""
    
    # Load model if different from current
    if request.model != "tts-1" and request.model in tts_service.available_models:
        tts_service.load_model(request.model)
    
    # Synthesize speech
    audio_data = tts_service.synthesize(
        text=request.input,
        voice=request.voice,
        speed=request.speed
    )
    
    # Return audio response
    media_type = "audio/wav"  # Coqui TTS outputs WAV by default
    if request.response_format == "mp3":
        media_type = "audio/mpeg"
    elif request.response_format == "opus":
        media_type = "audio/opus"
    
    return Response(
        content=audio_data,
        media_type=media_type,
        headers={
            "Content-Disposition": f"attachment; filename=speech.{request.response_format}"
        }
    )


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Coqui TTS FastAPI Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8881, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    
    args = parser.parse_args()
    
    uvicorn.run(
        "main:app",
        host=args.host,
        port=args.port,
        reload=args.reload
    )
