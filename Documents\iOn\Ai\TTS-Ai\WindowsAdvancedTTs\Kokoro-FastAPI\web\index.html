<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastKoko: Kokoro-based TTS</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/layout.css">
    <link rel="stylesheet" href="styles/header.css">
    <link rel="stylesheet" href="styles/forms.css">
    <link rel="stylesheet" href="styles/player.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link rel="stylesheet" href="styles/badges.css">
    <link rel="stylesheet" href="styles/controls.css">
    <script src="./siriwave.js"></script>
    <script async defer src="https://buttons.github.io/buttons.js"></script>
</head>
<body>
    <div class="sun">
        <div class="scanline"></div>
    </div>
    <div class="overlay"></div>
    <div class="grid-overlay"></div>
    <div class="badges-container">
        <a href="https://huggingface.co/hexgrad/Kokoro-82M" target="_blank" class="badge">
            <img src="https://img.shields.io/badge/Powered--by--HexGrad%2FKokoro--82M-black?logo=huggingface&logoColor=white&labelColor=black&style=for-the-badge" alt="HexGrad/Kokoro-82M on Hugging Face">
        </a>
        <div class="logo-container">
            <h1>FastKoko</h1>
            <div class="cup">
                <div class="handle"></div>
                <div class="steam"></div>
            </div>
        </div>
        <div class="badge">
            <a class="github-button" href="https://github.com/remsky/Kokoro-FastAPI" data-color-scheme="dark" data-size="large" data-show-count="true" aria-label="Star remsky/Kokoro-FastAPI on GitHub">Kokoro-FastAPI</a>
        </div>
    </div>
    <div class="container">
        <main>
            <div class="main-column">
                <div id="text-editor"></div>
                <div class="player-container">
                    <div class="player-controls">
                        <button id="play-pause-btn" class="player-btn">Play</button>
                        <input type="range" id="seek-slider" class="seek-slider" min="0" max="100" value="0">
                        <div class="volume-control">
                            <svg class="volume-icon" viewBox="0 0 24 24" width="20" height="20">
                                <path fill="currentColor" d="M12,4L9.91,6.09L12,8.18M4.27,3L3,4.27L7.73,9H3V15H7L12,20V13.27L16.25,17.53C15.58,18.04 14.83,18.46 14,18.7V20.77C15.38,20.45 16.63,19.82 17.68,18.96L19.73,21L21,19.73L12,10.73M19,12C19,12.94 18.8,13.82 18.46,14.64L19.97,16.15C20.62,14.91 21,13.5 21,12C21,7.72 18,4.14 14,3.23V5.29C16.89,6.15 19,8.83 19,12M16.5,12C16.5,10.23 15.5,8.71 14,7.97V10.18L16.45,12.63C16.5,12.43 16.5,12.21 16.5,12Z"/>
                            </svg>
                            <input type="range" id="volume-slider" class="volume-slider" min="0" max="100" value="100">
                        </div>
                        <span id="time-display" class="time-display">0:00</span>
                    </div>
                    <div id="wave-container" class="wave-container"></div>
                    <div class="download-placeholder"></div>
                    <div id="download-btn" class="download-button">
                        <div class="download-glow"></div>
                        <div class="download-icon">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M8 11L4 7h8l-4 4z" fill="currentColor"/>
                                <path d="M8 3v8M4 14h8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="controls">
                <div class="voice-select-container">
                    <div class="voice-search-wrapper">
                        <input 
                            type="text" 
                            id="voice-search" 
                            class="voice-search" 
                            placeholder="Search voices..."
                            autocomplete="off"
                        >
                        <div class="voice-dropdown" id="voice-dropdown">
                            <div class="voice-options" id="voice-options">
                                <!-- Voice options will be inserted here -->
                            </div>
                        </div>
                    </div>
                    <div class="selected-voices" id="selected-voices">
                        <!-- Selected voice tags will appear here -->
                    </div>
                </div>
                <div class="options">
                    <div class="speed-control">
                        <label for="speed-slider">Speed: <span id="speed-value">1.0</span>x</label>
                        <input type="range" id="speed-slider" min="0.1" max="4" step="0.1" value="1.0">
                    </div>
                    <div class="lang-control">
                        <label for="lang-select">Language:</label>
                        <select id="lang-select" class="lang-select">
                            <option value="">Auto</option>
                            <option value="e">Spanish</option>
                            <option value="a">English</option>
                            <option value="f">French</option>
                            <option value="h">Hindi</option>
                            <option value="i">Italian</option>
                            <option value="p">Portuguese</option>
                            <option value="j">Japanese</option>
                            <option value="z">Chinese</option>
                        </select>
                    </div>
                </div>
                <div class="button-group">
                    <button id="generate-btn">
                        <span class="btn-text">Generate Speech</span>
                        <span class="loader"></span>
                    </button>
                    <div class="generation-options">
                        <label>
                            <input type="checkbox" id="autoplay-toggle" checked>
                            Auto-play
                        </label>
                        <select id="format-select" class="format-select">
                            <option value="mp3">MP3</option>
                            <option value="wav">WAV</option>
                            <option value="pcm">PCM</option>
                        </select>
                    </div>
                    <button id="cancel-btn" class="player-btn cancel" style="display: none;">
                        Cancel
                    </button>
                </div>
                <div id="status" class="status"></div>
            </div>
        </main>
    </div>
    <script type="module" src="src/App.js"></script>
</body>
</html>
