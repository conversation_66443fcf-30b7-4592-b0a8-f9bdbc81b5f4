# Kokoro-FastAPI Configuration
# Copy this file to .env and customize as needed

# =============================================================================
# API Configuration
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8880
LOG_LEVEL=INFO

# =============================================================================
# Performance Settings
# =============================================================================
# CPU Optimization (adjust based on your CPU cores)
ONNX_NUM_THREADS=12
ONNX_INTER_OP_THREADS=6
OMP_NUM_THREADS=12
MKL_NUM_THREADS=12
NUMBA_NUM_THREADS=12

# ONNX Runtime Settings
ONNX_EXECUTION_MODE=parallel
ONNX_OPTIMIZATION_LEVEL=all
ONNX_MEMORY_PATTERN=true
ONNX_ARENA_EXTEND_STRATEGY=kNextPowerOfTwo

# =============================================================================
# Model Settings
# =============================================================================
MODEL_CACHE_DIR=/app/models
PRELOAD_MODEL=true
ENABLE_STREAMING=true

# Voice Settings
DEFAULT_VOICE=af_bella
VOICE_SPEED=1.0
VOICE_TEMPERATURE=0.7

# =============================================================================
# Audio Settings
# =============================================================================
DEFAULT_FORMAT=mp3
SAMPLE_RATE=24000
AUDIO_QUALITY=high

# =============================================================================
# Memory and Resource Management
# =============================================================================
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
MAX_CONCURRENT_REQUESTS=4
REQUEST_TIMEOUT=300

# =============================================================================
# Chunking Settings (for long text processing)
# =============================================================================
TARGET_MIN_TOKENS=175
TARGET_MAX_TOKENS=250
ABSOLUTE_MAX_TOKENS=450

# =============================================================================
# Security Settings
# =============================================================================
# Set to true in production
CORS_ENABLED=true
CORS_ORIGINS=["http://localhost:*", "http://127.0.0.1:*"]

# =============================================================================
# Integration Settings
# =============================================================================
# OpenAI API Compatibility
OPENAI_API_KEY=not-needed
OPENAI_BASE_URL=http://localhost:8880/v1

# For Open WebUI integration
OPENWEBUI_TTS_ENDPOINT=http://localhost:8880/v1/audio/speech

# For SillyTavern integration
SILLYTAVERN_TTS_URL=http://localhost:8880/v1/audio/speech

# =============================================================================
# Development Settings
# =============================================================================
DEBUG=false
ENABLE_PROFILING=false
SAVE_AUDIO_FILES=false

# =============================================================================
# Logging
# =============================================================================
LOG_FORMAT=json
LOG_FILE=/app/logs/kokoro.log
LOG_ROTATION=daily
LOG_RETENTION_DAYS=7
